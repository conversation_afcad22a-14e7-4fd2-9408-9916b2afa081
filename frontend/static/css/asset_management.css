/* Asset Management Specific Styles */

/* Page Header */
.page-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    margin-bottom: 1rem;
    box-shadow: 0 4px 15px var(--shadow-color);
}

.page-title {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.page-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 0;
}

/* Cards */
.search-card, .results-card {
    border: none;
    box-shadow: 0 2px 10px var(--shadow-color);
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.search-card:hover, .results-card:hover {
    box-shadow: 0 4px 20px var(--shadow-color);
}

/* Asset Item Cards */
.asset-item-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1rem;
    background: var(--card-bg);
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.asset-item-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition);
}

.asset-item-card:hover {
    box-shadow: 0 8px 25px var(--shadow-color);
    transform: translateY(-3px);
    border-color: var(--primary-color);
}

.asset-item-card:hover::before {
    opacity: 1;
}

/* Asset Header */
.asset-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.asset-item-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.asset-item-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
    flex-shrink: 0;
    box-shadow: 0 4px 10px rgba(var(--primary-color-rgb), 0.3);
}

.asset-item-details h5 {
    margin-bottom: 0.25rem;
    color: var(--text-color);
    font-weight: 600;
}

.asset-item-details .text-muted {
    font-size: 0.9rem;
}

/* Asset Actions */
.asset-item-actions {
    display: flex;
    gap: 0.5rem;
    flex-shrink: 0;
}

.asset-action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    font-size: 0.9rem;
}

.asset-action-btn:hover {
    transform: scale(1.1);
}

/* Asset Fields Grid */
.asset-item-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.asset-field {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid var(--border-color);
}

.asset-field-label {
    font-weight: 500;
    color: var(--text-color);
    opacity: 0.8;
}

.asset-field-value {
    font-weight: 600;
    color: var(--text-color);
}

/* Status Badges */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-OPERATING {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
    box-shadow: 0 2px 4px rgba(46, 204, 113, 0.3);
}

.status-ACTIVE {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.3);
}

.status-INACTIVE {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 2px 4px rgba(149, 165, 166, 0.3);
}

.status-BROKEN {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

.status-MISSING {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    box-shadow: 0 2px 4px rgba(243, 156, 18, 0.3);
}

/* Type Badges */
.type-badge {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    background: var(--light-color);
    color: var(--dark-color);
    border: 1px solid var(--border-color);
}

/* Loading States */
.loading-container {
    padding: 3rem 1rem;
}

.loading-container .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Table Styles */
.table th {
    background-color: var(--header-bg);
    color: var(--header-text-color);
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 1rem 0.75rem;
}

.table td {
    padding: 1rem 0.75rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--border-color);
}

.table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.05);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
        text-align: center;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .asset-item-card {
        padding: 1rem;
    }

    .asset-item-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .asset-item-actions {
        margin-top: 1rem;
        width: 100%;
        justify-content: space-around;
    }

    .asset-item-fields {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .asset-field {
        flex-direction: column;
        align-items: flex-start;
        padding: 0.75rem 0;
    }

    .asset-field-label {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .asset-field-value {
        font-size: 1rem;
    }
}

/* Dark Mode Support */
[data-bs-theme="dark"] .asset-item-card {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .table th {
    background-color: var(--header-bg);
    color: var(--header-text-color);
}

[data-bs-theme="dark"] .table td {
    border-bottom-color: var(--border-color);
}

[data-bs-theme="dark"] .table tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1);
}

/* Asset Detail Modal Styles */
.asset-detail-category {
    margin-bottom: 2rem;
}

.asset-detail-category-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.asset-detail-category-header i {
    margin-right: 0.5rem;
    color: var(--primary-color);
}

.asset-detail-category-header h6 {
    margin: 0;
    font-weight: 600;
    color: var(--text-color);
}

.asset-detail-fields {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.asset-detail-field {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: var(--light-color);
    border-radius: var(--border-radius);
    border-left: 4px solid var(--primary-color);
}

.asset-detail-field i {
    margin-right: 0.75rem;
    color: var(--primary-color);
    width: 20px;
    text-align: center;
}

.asset-detail-field-content {
    flex: 1;
}

.asset-detail-field-label {
    font-size: 0.8rem;
    color: var(--text-color);
    opacity: 0.7;
    margin-bottom: 0.25rem;
    text-transform: uppercase;
    font-weight: 500;
    letter-spacing: 0.5px;
}

.asset-detail-field-value {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.95rem;
}

/* Asset Actions Grid */
.asset-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.asset-action-card {
    padding: 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    background: var(--card-bg);
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.asset-action-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px var(--shadow-color);
    transform: translateY(-2px);
}

.asset-action-card i {
    font-size: 2rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.asset-action-card h6 {
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 600;
    user-select: none;
    pointer-events: none;
}

.asset-action-card p {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.7;
    margin: 0;
    user-select: none;
    pointer-events: none;
}

.asset-action-card i {
    user-select: none;
    pointer-events: none;
}

/* Ensure the entire button is clickable and text is not selectable */
.asset-action-card * {
    user-select: none;
    pointer-events: none;
}

.asset-action-card {
    position: relative;
}

.asset-action-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
}

/* Related Records Modal Styles */
#relatedRecordsModal .modal-dialog {
    max-width: 95vw;
}

#relatedRecordsModal .modal-body {
    max-height: 80vh;
    overflow: hidden;
}

/* Modern Side Navigation Styles */
.modern-side-nav {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 70vh;
    border-radius: 0.5rem 0 0 0.5rem;
    overflow: hidden;
}

.side-nav-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.side-nav-body {
    background-color: #ffffff;
}

.modern-nav-item {
    border: none !important;
    border-radius: 0.75rem !important;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef !important;
    color: #495057 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.5s;
}

.modern-nav-item:hover::before {
    left: 100%;
}

.modern-nav-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.15);
    border-color: #0d6efd !important;
    background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 100%);
}

.modern-nav-item.active {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
    color: white !important;
    border-color: #0d6efd !important;
    box-shadow: 0 4px 15px rgba(13, 110, 253, 0.3);
    transform: translateY(-1px);
}

.modern-nav-item.active .nav-title,
.modern-nav-item.active .nav-subtitle {
    color: white !important;
}

.modern-nav-item.active .nav-icon i {
    color: white !important;
}

.modern-nav-item.active .badge {
    background-color: rgba(255, 255, 255, 0.2) !important;
    color: white !important;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.nav-icon {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    background: linear-gradient(135deg, #e9ecef 0%, #f8f9fa 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modern-nav-item:hover .nav-icon {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    color: white;
    transform: scale(1.1);
}

.modern-nav-item.active .nav-icon {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.nav-icon i {
    font-size: 1.1rem;
    color: #6c757d;
    transition: all 0.3s ease;
}

.nav-title {
    font-size: 0.95rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.125rem;
}

.nav-subtitle {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1.2;
}

.nav-badge .badge {
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.35em 0.65em;
    border-radius: 50px;
    min-width: 1.5rem;
    text-align: center;
}

.side-nav-footer {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%) !important;
}

/* Tab Content Styles */
.tab-content {
    min-height: 70vh;
    max-height: 70vh;
    overflow-y: auto;
}

/* Table Styles */
.table-responsive {
    max-height: 55vh;
    overflow-y: auto;
}

.table th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 10;
    border-top: none;
}

/* Mobile Card Styles */
.mobile-card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.mobile-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transform: translateY(-1px);
}

/* Pagination Styles */
.pagination-info {
    font-size: 0.875rem;
    color: #6c757d;
}

.pagination .page-link {
    color: #0d6efd;
    border-color: #dee2e6;
}

.pagination .page-link:hover {
    color: #0a58ca;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    #relatedRecordsModal .modal-dialog {
        max-width: 100vw;
        margin: 0;
        height: 100vh;
    }

    #relatedRecordsModal .modal-content {
        height: 100vh;
        border-radius: 0;
    }

    #relatedRecordsModal .modal-body {
        max-height: calc(100vh - 120px);
        padding: 0;
    }

    .side-nav {
        min-height: auto;
        border-bottom: 1px solid #dee2e6;
    }

    .side-nav .nav-pills {
        flex-direction: row;
        overflow-x: auto;
        white-space: nowrap;
    }

    .side-nav .nav-pills .nav-link {
        flex: 0 0 auto;
        margin-right: 0.5rem;
        margin-bottom: 0;
    }

    .tab-content {
        min-height: calc(100vh - 200px);
        max-height: calc(100vh - 200px);
        padding: 1rem;
    }

    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }

    .pagination .page-item {
        margin: 0.125rem;
    }
}

/* Asset Status Cards */
.asset-status-card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
}

.asset-status-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

/* Badge Enhancements */
.badge {
    font-size: 0.75em;
    font-weight: 500;
}

/* Text Truncation */
.text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Mobile Create Dropdown Styles */
.mobile-create-dropdown {
    width: 100%;
    margin-top: 0.5rem;
}

.mobile-create-dropdown .mobile-action-btn {
    width: 100%;
    justify-content: center;
}

.mobile-action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.mobile-action-buttons .btn {
    width: 100%;
}

/* Desktop Actions Column Styles */
.btn-group .dropdown-menu {
    min-width: 250px;
    z-index: 1050 !important;
    position: absolute !important;
    will-change: transform;
}

.btn-group .dropdown-menu.show {
    z-index: 1050 !important;
    position: absolute !important;
}

.btn-group .dropdown-menu .dropdown-header {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c757d;
    padding: 0.25rem 1rem;
}

.btn-group .dropdown-menu .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.btn-group .dropdown-menu .dropdown-item:hover {
    background-color: #f8f9fa;
}

.btn-group .dropdown-menu .dropdown-item i {
    width: 1.2rem;
    text-align: center;
}

/* Table row positioning for dropdowns */
.table tbody tr {
    position: relative;
}

.table tbody tr td {
    position: relative;
}

/* Ensure dropdown container has proper positioning */
.table .btn-group {
    position: static;
}

.table .btn-group.show .dropdown-menu {
    z-index: 1050 !important;
    position: fixed !important;
}

/* Force dropdown menus to appear above everything */
.dropdown-menu {
    z-index: 1050 !important;
}

.dropdown-menu.show {
    z-index: 1050 !important;
    position: absolute !important;
}

/* Ensure table doesn't create stacking context issues */
.table-responsive {
    overflow: visible !important;
}

.table {
    overflow: visible !important;
}

/* Additional dropdown positioning fixes */
.btn-group .dropdown-toggle::after {
    margin-left: 0.255em;
}

.btn-group .dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .mobile-action-buttons {
        padding: 1rem 0;
    }

    .mobile-create-dropdown .dropdown-menu {
        width: 100%;
        max-width: none;
    }
}
