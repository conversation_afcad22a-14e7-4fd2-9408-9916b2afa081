#!/usr/bin/env python3
"""
Asset Creation Service

This service handles the creation of assets, work orders, service requests, 
incidents, and PM schedules using the appropriate Maximo REST API endpoints.

Based on investigation findings:
- MXAPIASSET does NOT have functional Create WSMethods
- Must use direct POST operations to appropriate endpoints:
  * Assets: POST /oslc/os/mxapiasset
  * Work Orders: POST /oslc/os/mxapiwodetail  
  * Service Requests: POST /oslc/os/mxapisr
  * Incidents: POST /oslc/os/mxapiincident (or similar)
  * PM Schedules: POST /oslc/os/mxapipm (or similar)

Author: Augment Agent
Date: 2025-01-27
"""

import logging
import requests
from typing import Dict, Any, Optional
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class AssetCreationService:
    """Service for creating assets and related records in Maximo."""
    
    def __init__(self, token_manager):
        """
        Initialize the Asset Creation Service.
        
        Args:
            token_manager: Token manager instance for authentication
        """
        self.token_manager = token_manager
        self.logger = logger
        
    def create_asset(self, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new asset using MXAPIASSET endpoint.
        
        Args:
            asset_data: Asset data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 ASSET CREATION: Creating asset {asset_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiasset"
            
            # Prepare asset payload
            payload = {
                'assetnum': asset_data.get('assetnum'),
                'siteid': asset_data.get('siteid'),
                'description': asset_data.get('description'),
                'status': asset_data.get('status', 'OPERATING'),
                'location': asset_data.get('location', ''),
                'assettype': asset_data.get('assettype', 'PRODUCTION')
            }
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                timeout=(5.0, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code in [200, 201]:
                self.logger.info(f"✅ ASSET CREATION: Successfully created asset {asset_data.get('assetnum')}")
                return {
                    'success': True,
                    'message': f"Asset {asset_data.get('assetnum')} created successfully",
                    'data': response.json() if response.text else {}
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.logger.error(f"❌ ASSET CREATION: Failed - {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Asset creation failed: {str(e)}"
            self.logger.error(f"❌ ASSET CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def create_work_order(self, wo_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new work order using MXAPIWODETAIL endpoint.
        
        Args:
            wo_data: Work order data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 WORK ORDER CREATION: Creating work order for asset {wo_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            # Prepare work order payload
            payload = {
                'assetnum': wo_data.get('assetnum'),
                'siteid': wo_data.get('siteid'),
                'description': wo_data.get('description'),
                'worktype': wo_data.get('worktype', 'CM'),
                'priority': wo_data.get('priority', '3'),
                'status': 'WAPPR',  # Waiting for Approval
                'longdescription': wo_data.get('longdescription', ''),
                'targstartdate': wo_data.get('targstartdate', '')
            }
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                timeout=(5.0, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code in [200, 201]:
                self.logger.info(f"✅ WORK ORDER CREATION: Successfully created work order for asset {wo_data.get('assetnum')}")
                return {
                    'success': True,
                    'message': f"Work order created successfully for asset {wo_data.get('assetnum')}",
                    'data': response.json() if response.text else {}
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.logger.error(f"❌ WORK ORDER CREATION: Failed - {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Work order creation failed: {str(e)}"
            self.logger.error(f"❌ WORK ORDER CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def create_service_request(self, sr_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new service request using MXAPISR endpoint.
        
        Args:
            sr_data: Service request data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 SERVICE REQUEST CREATION: Creating service request for asset {sr_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapisr"
            
            # Prepare service request payload
            payload = {
                'assetnum': sr_data.get('assetnum'),
                'siteid': sr_data.get('siteid'),
                'description': sr_data.get('description'),
                'priority': sr_data.get('priority', '3'),
                'status': 'NEW',
                'longdescription': sr_data.get('longdescription', ''),
                'requestedby': sr_data.get('requestedby', ''),
                'requesteddate': sr_data.get('requesteddate', '')
            }
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                timeout=(5.0, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code in [200, 201]:
                self.logger.info(f"✅ SERVICE REQUEST CREATION: Successfully created service request for asset {sr_data.get('assetnum')}")
                return {
                    'success': True,
                    'message': f"Service request created successfully for asset {sr_data.get('assetnum')}",
                    'data': response.json() if response.text else {}
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.logger.error(f"❌ SERVICE REQUEST CREATION: Failed - {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Service request creation failed: {str(e)}"
            self.logger.error(f"❌ SERVICE REQUEST CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def create_incident(self, incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new incident using appropriate incident endpoint.
        
        Args:
            incident_data: Incident data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 INCIDENT CREATION: Creating incident for asset {incident_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            # Try different possible incident endpoints
            possible_endpoints = [
                f"{base_url}/oslc/os/mxapiincident",
                f"{base_url}/oslc/os/mxapisr",  # Fallback to service request
                f"{base_url}/oslc/os/mxapiwodetail"  # Fallback to work order
            ]
            
            # Prepare incident payload
            payload = {
                'assetnum': incident_data.get('assetnum'),
                'siteid': incident_data.get('siteid'),
                'description': incident_data.get('description'),
                'priority': incident_data.get('priority', '1'),
                'status': 'NEW',
                'longdescription': incident_data.get('details', ''),
                'reportedby': incident_data.get('reportedby', ''),
                'reporteddate': incident_data.get('incidentdate', '')
            }
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}
            
            # Try each endpoint until one works
            for api_url in possible_endpoints:
                try:
                    response = self.token_manager.session.post(
                        api_url,
                        json=payload,
                        timeout=(5.0, 30),
                        headers={
                            "Accept": "application/json",
                            "Content-Type": "application/json"
                        }
                    )
                    
                    if response.status_code in [200, 201]:
                        endpoint_type = api_url.split('/')[-1]
                        self.logger.info(f"✅ INCIDENT CREATION: Successfully created incident using {endpoint_type}")
                        return {
                            'success': True,
                            'message': f"Incident created successfully for asset {incident_data.get('assetnum')} using {endpoint_type}",
                            'data': response.json() if response.text else {},
                            'endpoint_used': endpoint_type
                        }
                except Exception as e:
                    self.logger.warning(f"⚠️ INCIDENT CREATION: Failed with {api_url}: {str(e)}")
                    continue
            
            # If all endpoints failed
            error_msg = "All incident creation endpoints failed"
            self.logger.error(f"❌ INCIDENT CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Incident creation failed: {str(e)}"
            self.logger.error(f"❌ INCIDENT CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
